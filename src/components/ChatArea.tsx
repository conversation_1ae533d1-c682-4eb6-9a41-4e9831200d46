import React from "react";
import { MessageBubble } from "./MessageBubble";
import type { ConversationMessage } from "../utils/conversationUtils";

interface ChatAreaProps {
  messages: ConversationMessage[];
  isActive: boolean;
  isGameStarted: boolean;
  initialMessage?: string;
}

/**
 * Chat area component that displays conversation messages
 *
 * Features:
 * - Scrollable message container
 * - Empty state with helpful instructions
 * - Auto-scroll to latest message
 * - Responsive design
 */
export const ChatArea: React.FC<ChatAreaProps> = ({
  messages,
  isActive,
  isGameStarted,
  initialMessage
}) => {
  /**
   * Get appropriate empty state message based on current state
   */
  const getEmptyStateMessage = () => {
    if (isActive) {
      return "¡Conversación activa! Puedes empezar a hablar";
    }
    if (isGameStarted && initialMessage) {
      return "Activando chat de voz automáticamente...";
    }
    return "Inicia la conversación para comenzar a chatear";
  };

  return (
    <>
      {/* CSS for animations */}
      <style>{`
        @keyframes slideIn {
          from {
            opacity: 0;
            transform: translateY(10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
      `}</style>

      <div
        style={{
          backgroundColor: "#f8f9fa",
          border: "1px solid #e9ecef",
          borderRadius: "12px",
          padding: "20px",
          minHeight: "300px",
          maxHeight: "450px",
          overflowY: "auto",
          scrollBehavior: "smooth",
        }}
      >
        {messages.length === 0 ? (
          <EmptyState message={getEmptyStateMessage()} />
        ) : (
          <MessageList messages={messages} />
        )}
      </div>
    </>
  );
};

/**
 * Empty state component when no messages are present
 */
interface EmptyStateProps {
  message: string;
}

const EmptyState: React.FC<EmptyStateProps> = ({ message }) => (
  <div
    style={{
      textAlign: "center",
      color: "#6c757d",
      fontSize: "16px",
      paddingTop: "60px",
    }}
  >
    <div style={{ fontSize: "48px", marginBottom: "16px" }}>💬</div>
    <p style={{ margin: "8px 0" }}>{message}</p>
    <p
      style={{
        fontSize: "13px",
        opacity: 0.8,
        marginTop: "12px",
      }}
    >
      El micrófono se gestiona automáticamente
    </p>
  </div>
);

/**
 * Message list component that renders all messages with auto-scroll
 */
interface MessageListProps {
  messages: ConversationMessage[];
}

const MessageList: React.FC<MessageListProps> = ({ messages }) => (
  <>
    {messages.map((message) => (
      <MessageBubble key={message.id} message={message} />
    ))}
    {/* Auto-scroll anchor */}
    <div
      style={{ height: "1px" }}
      ref={(el) => {
        if (el && messages.length > 0) {
          el.scrollIntoView({ behavior: "smooth" });
        }
      }}
    />
  </>
);
