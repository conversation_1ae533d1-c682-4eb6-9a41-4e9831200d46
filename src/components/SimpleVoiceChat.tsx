// React core
import { useEffect, useRef, useState } from "react";
// Third-party library imports
// Services
import type { GameProgress as GameProgressType } from "../services/ConversationStorage";
// Components
import { ChatArea } from "./ChatArea";
import { ConversationStorage } from "../services/ConversationStorage";
// Utils & Constants & Helpers
import { useRealTimeConversation } from "../hooks/useRealTimeConversation";
// Styles

const UnsupportedBrowserMessage = () => (
  <div className="unsupported-browser-warning">
    <h4>⚠️ Reconocimiento de voz no disponible</h4>
    <p>Tu navegador no soporta esta funcionalidad</p>
  </div>
);

interface SimpleVoiceChatProps {
  generatedCharacter?: string;
  isGameStarted: boolean;
  initialMessage?: string;
  onGameEnd?: (gameWon: boolean) => void;
}

export const SimpleVoiceChat: React.FC<SimpleVoiceChatProps> = ({
  generatedCharacter,
  isGameStarted,
  initialMessage,
  onGameEnd,
}) => {
  // State for game management
  const [gameProgress, setGameProgress] = useState<GameProgressType | null>(null);
  const gameEndTriggered = useRef(false);

  // Refs
  const conversationStorage = useRef(ConversationStorage.getInstance());

  // Hook for real-time conversation management
  const {
    isActive,
    messages,
    isSupported,
    error,
    stopConversation,
    addInitialMessage,
  } = useRealTimeConversation(
    generatedCharacter,
    isGameStarted,
    gameProgress?.gameFinished
  );

  /**
   * Add initial message when it arrives
   */
  useEffect(() => {
    if (initialMessage && isGameStarted) {
      addInitialMessage(initialMessage);
    }
  }, [initialMessage, isGameStarted, addInitialMessage]);

  /**
   * Update game progress when session changes
   */
  useEffect(() => {
    if (!isGameStarted) return;

    const updateProgress = () => {
      const progress = conversationStorage.current.getGameProgress();
      setGameProgress(progress);

      // Notify parent when game finishes (only once)
      if (progress?.gameFinished && !gameEndTriggered.current && onGameEnd) {
        console.log("🎮 Game finished detected:", {
          gameFinished: progress.gameFinished,
          gameWon: progress.gameWon,
        });
        gameEndTriggered.current = true;
        onGameEnd(progress.gameWon);
      }
    };

    updateProgress();
    const interval = setInterval(updateProgress, 1000);
    return () => clearInterval(interval);
  }, [isGameStarted, messages, onGameEnd]);

  /**
   * Reset flags when game restarts
   */
  useEffect(() => {
    if (!isGameStarted) {
      gameEndTriggered.current = false;
    }
  }, [isGameStarted]);

  // Helper to check if game has finished
  const isGameFinished = gameProgress?.gameFinished ?? false;

  /**
   * Stop conversation when game finishes
   */
  useEffect(() => {
    if (isGameFinished && isActive) {
      console.log("🔇 Game finished, stopping conversation");
      stopConversation();
    }
  }, [isGameFinished, isActive, stopConversation]);

  if (!isGameStarted) {
    return null;
  }

  if (!isSupported) {
    return <UnsupportedBrowserMessage />;
  }

  return (
    <div className="voice-chat-container">
      {error && <div className="error-message">❌ {error}</div>}

      <ChatArea
        messages={messages}
        isActive={isActive}
        isGameStarted={isGameStarted}
        initialMessage={initialMessage}
      />
    </div>
  );
};
