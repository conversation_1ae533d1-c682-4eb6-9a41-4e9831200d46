import React from "react";
// import "./GameLive.scss";
import { Modal } from "microapps";

/**
 * Game Live Component
 *
 * Displays the current lives/attempts remaining in the game.
 * Features:
 * - Visual representation of remaining lives
 * - Game progress information
 * - Lives counter
 */
export const GameLive: React.FC = () => {
  return (
    <Modal
      title="Tus preguntas restantes"
      // onClose={() => } // setIsLivesPopUpShown((prev) => !prev)
      // onConfirm={() => } // setIsLivesPopUpShown((prev) => !prev)
      confirmText="Entendido"
      body=" Tienes un máximo de 20 preguntas para adivinar la respuesta. Cada
          vez que haces una, se descuenta del contador. Piensa bien cada
          pregunta: ¡cada una cuenta!"
    />
  );
};
