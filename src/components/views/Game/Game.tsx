// React core
import React from "react";
// Third-party library imports
import { Image } from "microapps";
// Services
// Components
import { SimpleVoiceChat } from "../../SimpleVoiceChat";
import { MicAdapter } from "../../MicAdapter";
import { Chat<PERSON><PERSON> } from "../../ChatArea";
// Utils & Constants & Helpers
// Hooks
import { useRealTimeConversation } from "../../../hooks/useRealTimeConversation";
// Styles
import "./Game.scss";

interface GameProps {
  generatedCharacter: string;
  gameStarted: boolean;
  isGameStarted: boolean;
  initialMessage: string;
  onGameEnd: (gameWon: boolean) => void;
  onShowGameLive: () => void;
  onShowGameHint: () => void;
  onShowGameExit: () => void;
}

export const Game: React.FC<GameProps> = ({
  generatedCharacter,
  gameStarted,
  isGameStarted,
  initialMessage,
  onGameEnd,
  onShowGameLive,
  onShowGameHint,
  onShowGameExit,
}) => {
  // Hook for real-time conversation management to get mic-related data
  const {
    isActive,
    conversationState,
    messages,
    error,
    startConversation,
    stopConversation,
    enableSmartMicrophone,
  } = useRealTimeConversation(
    generatedCharacter,
    isGameStarted,
    false
  );

  // Handle voice button click - start/stop conversation
  const handleVoiceToggle = () => {
    if (isActive) {
      stopConversation();
    } else {
      startConversation().then((success) => {
        if (success) {
          enableSmartMicrophone();
        }
      });
    }
  };
  return (
    <div className="view-game">
      <div className="container">
        <div className="menu-left">
          <div className="enygma-logo">
            <Image
              src="assets/game/enygma.png"
              alt="Enygma"
              className="enygma-image"
              width="180px"
              aspectRatio="1:1"
            />

            <div className="speaking">
              <MicAdapter
                conversationState={conversationState}
                isVoiceActive={isActive}
                micLevel={0}
                onToggle={handleVoiceToggle}
                voiceError={error}
                id="game-mic"
                className="game-mic-wrapper"
              />
            </div>
          </div>
        </div>

        <div>
          <div className="character-title">
            <span>{generatedCharacter}</span>
          </div>

          {/* <SimpleVoiceChat
            generatedCharacter={generatedCharacter}
            isGameStarted={isGameStarted}
            initialMessage={initialMessage}
            onGameEnd={onGameEnd}
          /> */}

          <ChatArea
            messages={messages}
            isActive={isActive}
            isGameStarted={isGameStarted}
            initialMessage={initialMessage}
          />
        </div>

        {(generatedCharacter || gameStarted) && (
          <div className="menu-right">
            <div className="game-navigation">
              <div onClick={onShowGameLive} className="image-button">
                <Image
                  width="100%"
                  aspectRatio="1:1"
                  src="assets/game/lives.png"
                  alt="Vidas"
                  className="book-image"
                />
              </div>

              <div onClick={onShowGameHint} className="image-button">
                <Image
                  width="100%"
                  aspectRatio="1:1"
                  src="assets/game/clues.png"
                  alt="Pistas"
                  className="clues-image"
                />
                <p className="body2 bold">Pistas</p>
              </div>

              <div onClick={onShowGameExit} className="image-button">
                <Image
                  width="100%"
                  aspectRatio="1:1"
                  src="assets/game/exit.png"
                  alt="Salir"
                  className="exit-image"
                />
                <p className="body2 bold">Salir</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
