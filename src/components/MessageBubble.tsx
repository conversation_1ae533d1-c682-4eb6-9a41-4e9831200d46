import React from "react";
import type { ConversationMessage } from "../utils/conversationUtils";

interface MessageBubbleProps {
  message: ConversationMessage;
}

/**
 * Message bubble component for displaying conversation messages
 *
 * Features:
 * - Different styling for user vs AI messages
 * - Support for interim (temporary) messages
 * - Timestamp display
 * - Smooth animations
 */
export const MessageBubble: React.FC<MessageBubbleProps> = ({ message }) => {
  const isUser = message.type === "user";

  return (
    <div
      style={{
        display: "flex",
        justifyContent: isUser ? "flex-end" : "flex-start",
        marginBottom: "16px",
        animation: "slideIn 0.3s ease-out",
      }}
    >
      <div
        style={{
          maxWidth: "85%",
          padding: "14px 18px",
          borderRadius: isUser ? "20px 20px 4px 20px" : "20px 20px 20px 4px",
          backgroundColor: isUser ? "#002332" : "#002332",
          color: isUser ? "white" : "#333",
          fontSize: "15px",
          lineHeight: "1.4",
          boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
          opacity: message.isInterim ? 0.7 : 1,
          border: message.isInterim ? "2px dashed #007bff" : "none",
          position: "relative",
        }}
      >
        {/* Message content */}
        <div style={{ marginBottom: message.isInterim ? "8px" : "4px" }}>
          {message.content}
        </div>

        {/* Interim message indicator */}
        {message.isInterim && (
          <div
            style={{
              fontSize: "11px",
              opacity: 0.8,
              fontStyle: "italic",
              color: isUser ? "#e3f2fd" : "#666",
            }}
          >
            Escribiendo...
          </div>
        )}

        {/* Timestamp */}
        <div
          style={{
            fontSize: "11px",
            opacity: 0.7,
            marginTop: "6px",
            textAlign: "right",
            color: isUser ? "#e3f2fd" : "#888",
          }}
        >
          {message.timestamp.toLocaleTimeString([], {
            hour: "2-digit",
            minute: "2-digit",
          })}
        </div>
      </div>
    </div>
  );
};
